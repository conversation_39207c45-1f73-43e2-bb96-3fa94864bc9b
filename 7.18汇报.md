**Momentum-Gated Dynamic Routing:**

**Enhancing Temporal Consistency in End-to-End Autonomous Driving**

**动量门控动态路由：**

**提升端到端自动驾驶中的时序一致性**

![](https://cdn.nlark.com/yuque/0/2025/png/********/*************-d2195878-284e-43e3-8fa5-4fe721a77dd3.png)

一、研究现状

（一）端到端自动驾驶技术发展

端到端自动驾驶技术成为研究热点。它将自动驾驶中的感知、规划、控制等任务统一在一个框架内，通过直接对传感器数据进行处理来输出车辆控制信号，摒弃了传统模块化方法中各模块间复杂的交互过程，使得系统更加简洁高效，能更好地适应复杂多变的驾驶场景。

（二）面临的挑战

**历史信息利用不充分**：现有端到端自动驾驶方法在处理历史信息时存在缺陷。如在相关代码实现中，historical_motion_states仅包含位置坐标（x, y, z），未保存速度、加速度、方向盘角度等动态状态信息。这导致模型无法充分学习运动连续性，难以有效利用历史信息进行准确的轨迹预测和规划，使得决策缺乏连贯性和稳定性。

**注意力机制不合理**：在注意力机制方面，当前方法的注意力权重完全由当前特征与历史特征的相似度决定，未引入运动学约束，且时间衰减因子仅基于时间距离，与运动状态无关。这种机制无法充分考虑车辆运动的物理特性，导致信息融合不够准确，影响了对复杂场景的适应性和决策的准确性。

**规划查询缺乏连贯性**：每个时间步的规划查询独立生成，不依赖前一时间步的规划结果，历史信息融合仅基于当前特征，未考虑历史规划轨迹的连续性。这使得规划过程缺乏对过去经验的有效利用，无法保证规划的一致性和稳定性，在复杂场景下容易出现规划失误。

二、要解决的具体问题

**解决端到端自动驾驶中动态路由的时序连贯性缺失问题。**

解决由于历史信息利用不充分、注意力机制不合理以及规划查询缺乏连贯性所导致的轨迹预测不准确、规划不稳定等问题，从而提升自动驾驶系统在复杂场景下的决策能力和行驶安全性。

三、具体思路

在BridgeAD的动态信息路由模块中，嵌入动量门控单元融合BridgeAD和MomAD的优势

（一）融合技术优势

BridgeAD 优势利用：BridgeAD 提供动态信息路由，通过<font style="background-color:yellow;">多步查询分解</font>和时间步分配，实现历史未来信息高效传递，使模型能够更好地捕捉时序依赖，增强感知与运动规划的连贯性。

MomAD 优势利用：MomAD 的轨迹动量机制（TTM 匹配 + MPI 交互器）提供运动物理先验，如速度、加速度连续性约束，确保轨迹的稳定性和时序一致性。

（二）具体实现步骤

**1. 特征提取：**基于 MomAD 的拓扑轨迹匹配（TTM），从历史轨迹中提取包含速度方向、加速度变化率等物理量的轨迹动量向量，形成与时间步对应的动量特征序列 

![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813217761-c490f1f6-7d6a-416a-af14-52b499d53795.png)

这些动量特征向量能够反映车辆运动的动态特性，为后续的权重计算提供重要依据。

**2.动量感知联合加权：**对每个时间步的历史信息 ![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813217977-dfa8b944-91ac-4ad7-9884-675efca7f80e.png)，计算动量相似度权重 

![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813218164-8c759409-b7f4-4bbe-a02f-87c317ba6616.png)

并与原动态路由的感知权重 ![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813218343-f6a99fa3-0d93-4736-90e2-d65fffcbb211.png)

融合为最终权重 

![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813218533-03518161-eed5-43ed-873a-2d5d8cb32a15.png)

其中 α 为动量约束系数。通过这种方式，实现了历史信息与动量信息的有机融合，使得模型在进行信息融合时能够同时考虑感知特征和运动物理特性，提高信息融合的准确性和合理性。

**3. 正则化：**在训练阶段引入正则项 

![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813218686-941c7eab-e7b4-4c90-bf88-4f0a562112cf.png)

强制路由后的信息变化率与实际动量变化率一致

前者历史动量差分，后者为路由后预测动量差分）。这一正则化项的引入有助于优化模型训练过程，使模型更加准确地学习到车辆运动的规律，提高模型的泛化能力和稳定性。



四、结合图片流程的详细介绍

![](https://cdn.nlark.com/yuque/0/2025/png/********/1754813218946-293942b6-607f-4a65-a88f-a33f8b9d8ca2.png)

（一）数据输入与初步处理

多视图图像作为输入，首先进入 BridgeAD 的图像编码器进行处理，生成用于感知的特征。同时，历史信息被提取出来，但在传统方法中，历史信息存在如前所述的不完整问题。我们通过改进，基于 MomAD 的 TTM 模块对历史轨迹进行处理，提取更全面的动量特征。

（二）动量门控单元处理

在动量门控单元中，将提取的动量特征与历史信息进行融合。通过计算动量相似度权重 w<sub>m</sub> 和感知权重 w<sub>s</sub> 并根据动量约束系数 α 得到最终权重 w。这一权重将用于对历史信息进行加权聚合，使得融合后的信息既包含了丰富的感知内容，又考虑了车辆运动的物理特性，从而增强了信息的时序连贯性。

（三）动态路由与规划

经过动量门控单元处理后的信息，进入 BridgeAD 的动态路由模块。该模块通过多步查询分解和时间步分配，将信息合理地传递到后续的运动规划模块。在运动规划模块中，结合融合后的信息进行轨迹预测和规划，生成更加准确、稳定的规划结果。

（四）输出与反馈

最终的规划结果输出用于车辆的控制。同时，在训练过程中，通过引入的正则化项，将预测的动量变化与实际动量变化进行对比，反馈调整模型参数，不断优化模型性能，提高轨迹预测和规划的准确性及时序一致性。

五、总结

本研究项目针对端到端自动驾驶中动态路由的时序连贯性缺失问题，提出了创新的解决方案。通过融合 BridgeAD 和 MomAD 的优势，引入动量门控单元，实现了对历史信息的更有效利用、更合理的注意力机制以及更连贯的规划查询。



